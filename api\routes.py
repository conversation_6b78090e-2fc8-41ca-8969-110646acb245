# -*- coding: utf-8 -*-
"""
مسارات API - API Routes
"""
from flask import jsonify, request, session
from api import bp
from api.schemas import (
    categories_schema, category_schema, 
    products_schema, product_schema,
    settings_schema
)
from models import Category, SubCategory, Product, Settings


@bp.route('/menu')
def get_menu():
    """الحصول على القائمة الكاملة - Get full menu"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    
    # الحصول على الفئات النشطة مع المنتجات - Get active categories with products
    categories = Category.query.filter_by(is_active=True)\
                              .order_by(Category.sort_order).all()
    
    # تصفية المنتجات النشطة فقط - Filter only active products
    for category in categories:
        category.products = [p for p in category.products if p.is_active]
        for subcategory in category.subcategories:
            if subcategory.is_active:
                subcategory.products = [p for p in subcategory.products if p.is_active]
    
    # تسلسل البيانات - Serialize data
    result = categories_schema.dump(categories, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang
    })


@bp.route('/categories')
def get_categories():
    """الحصول على قائمة الفئات - Get categories list"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    
    categories = Category.query.filter_by(is_active=True)\
                              .order_by(Category.sort_order).all()
    
    result = categories_schema.dump(categories, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang
    })


@bp.route('/categories/<int:category_id>')
def get_category(category_id):
    """الحصول على فئة محددة - Get specific category"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    
    category = Category.query.filter_by(id=category_id, is_active=True).first()
    
    if not category:
        return jsonify({
            'success': False,
            'message': 'Category not found'
        }), 404
    
    # تصفية المنتجات النشطة فقط - Filter only active products
    category.products = [p for p in category.products if p.is_active]
    
    result = category_schema.dump(category, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang
    })


@bp.route('/products')
def get_products():
    """الحصول على قائمة المنتجات - Get products list"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    category_id = request.args.get('category_id', type=int)
    subcategory_id = request.args.get('subcategory_id', type=int)
    featured_only = request.args.get('featured', type=bool, default=False)
    
    # بناء الاستعلام - Build query
    query = Product.query.filter_by(is_active=True)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if subcategory_id:
        query = query.filter_by(subcategory_id=subcategory_id)
    
    if featured_only:
        query = query.filter_by(is_featured=True)
    
    products = query.order_by(Product.sort_order).all()
    
    result = products_schema.dump(products, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang,
        'count': len(result)
    })


@bp.route('/products/<int:product_id>')
def get_product(product_id):
    """الحصول على منتج محدد - Get specific product"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    
    product = Product.query.filter_by(id=product_id, is_active=True).first()
    
    if not product:
        return jsonify({
            'success': False,
            'message': 'Product not found'
        }), 404
    
    result = product_schema.dump(product, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang
    })


@bp.route('/settings')
def get_settings():
    """الحصول على إعدادات المتجر - Get shop settings"""
    settings = Settings.get_settings()
    
    result = settings_schema.dump(settings)
    
    return jsonify({
        'success': True,
        'data': result
    })


@bp.route('/search')
def search_products():
    """البحث في المنتجات - Search products"""
    lang = request.args.get('lang', session.get('lang', 'ar'))
    query = request.args.get('q', '').strip()
    
    if not query:
        return jsonify({
            'success': False,
            'message': 'Search query is required'
        }), 400
    
    # البحث في أسماء المنتجات والأوصاف - Search in product names and descriptions
    if lang == 'ar':
        products = Product.query.filter(
            Product.is_active == True,
            (Product.name_ar.contains(query) | 
             Product.description_ar.contains(query) |
             Product.ingredients_ar.contains(query))
        ).order_by(Product.sort_order).all()
    else:
        products = Product.query.filter(
            Product.is_active == True,
            (Product.name_en.contains(query) | 
             Product.description_en.contains(query) |
             Product.ingredients_en.contains(query))
        ).order_by(Product.sort_order).all()
    
    result = products_schema.dump(products, context={'lang': lang})
    
    return jsonify({
        'success': True,
        'data': result,
        'language': lang,
        'query': query,
        'count': len(result)
    })
